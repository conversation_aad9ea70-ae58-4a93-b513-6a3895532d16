version: 2
updates:
  - package-ecosystem: pip
    directory: "/"
    schedule:
      interval: daily
    open-pull-requests-limit: 50
    groups:
      all-pip-deps:
        patterns:
          - "*"

  - package-ecosystem: docker
    directory: "/"
    schedule:
      interval: daily
    open-pull-requests-limit: 50
    groups:
      all-docker-deps:
        patterns:
          - "*"

  - package-ecosystem: github-actions
    directory: "/"
    schedule:
      interval: daily
    open-pull-requests-limit: 50
    groups:
      all-gha-deps:
        patterns:
          - "*"