# Amusic Bot - Comprehensive Documentation

## Overview
Amusic is a powerful Telegram music bot built with Python using Pyrogram and PyTgCalls. The bot provides high-quality music streaming capabilities in Telegram voice chats with support for multiple platforms and advanced administrative controls.

## Key Features

### 🎵 Music Playback
- **Multi-Platform Support**: Play music from YouTube, Spotify, Apple Music, SoundCloud, Saavn, and Resso
- **High-Quality Audio**: Supports multiple audio bitrates (64k, 128k, 256k, 320k)
- **Video Streaming**: Play videos in voice chats with quality options (360p to 4K)
- **Live Streaming**: Support for live streams and radio stations
- **Playlist Support**: Create and manage playlists, import from Spotify/Apple Music
- **Queue Management**: Advanced queue system with shuffle, loop, and skip functionality

### 🎛️ Admin Controls
- **Pause/Resume**: Control playback with admin commands
- **Skip**: Skip to next track in queue
- **Stop**: Stop current playback and clear queue
- **Loop**: Set loop mode (1-10 times or infinite)
- **Seek**: Jump to specific time in track
- **Mute/Unmute**: Control audio output
- **Shuffle**: Randomize queue order
- **Volume Control**: Adjust playback volume

### 👥 User Management
- **Authorization System**: Control who can use the bot in groups
- **Sudo Users**: Grant elevated permissions to trusted users
- **Ban System**: Global and local user banning capabilities
- **Private Mode**: Restrict bot usage to authorized chats only

### 🔧 Advanced Settings
- **Play Mode**: Configure who can add songs (Everyone/Admins only)
- **Play Type**: Set audio or video playback preference
- **Clean Mode**: Auto-delete bot messages after specified time
- **Channel Play**: Play music in linked channels
- **Auto Leave**: Assistant leaves inactive chats automatically
- **Language Support**: Arabic (primary) and English (fallback)

### 🛠️ Administrative Tools
- **Statistics**: Detailed bot usage statistics
- **Active Chats**: Monitor currently active voice chats
- **Backup**: Database backup and restore functionality
- **Maintenance Mode**: Temporarily disable bot for maintenance
- **Speed Test**: Check server connection speed
- **Logs**: Comprehensive logging system

### 🎤 Additional Features
- **Lyrics**: Fetch and display song lyrics
- **Song Download**: Download audio files (MP3/MP4)
- **Top Tracks**: Play trending songs from various platforms
- **Search**: Advanced search across multiple music platforms
- **Inline Mode**: Use bot in inline mode for quick searches

## Technical Specifications

### Database
- **Redis**: Local Redis database with bot ID-based isolation
- **Multi-Instance Support**: Multiple bot instances can run independently
- **Data Persistence**: User preferences, playlists, and settings stored locally

### Deployment
- **Ubuntu Server**: Optimized for Ubuntu server deployment
- **No Cloud Dependencies**: Runs entirely on local infrastructure
- **Scalable**: Supports multiple concurrent voice chats

### Audio Quality
- **Bitrate Options**: 64k, 128k, 256k, 320k kbps
- **Video Quality**: 360p, 480p, 720p, 1080p, 2K, 4K
- **Low Latency**: Optimized for real-time streaming

### Platform Integration
- **YouTube**: Direct streaming and search
- **Spotify**: Track search and playlist import
- **Apple Music**: Song search and streaming
- **SoundCloud**: Independent artist support
- **Saavn**: Indian music platform
- **Resso**: Social music platform

## Commands Overview

### Basic Commands
- `/play [song name/URL]` - Play music
- `/stream [URL]` - Stream live content
- `/pause` - Pause playback
- `/resume` - Resume playback
- `/skip` - Skip current track
- `/stop` - Stop and clear queue

### Queue Management
- `/queue` - Show current queue
- `/shuffle` - Shuffle queue
- `/loop [1-10]` - Set loop count
- `/seek [time]` - Seek to specific time

### Settings
- `/settings` - Bot configuration panel
- `/playmode` - Configure play permissions
- `/language` - Change bot language

### Information
- `/stats` - Bot statistics
- `/ping` - Check bot latency
- `/lyrics [song]` - Get song lyrics

## Installation Requirements
- Python 3.8+
- Redis Server
- FFmpeg
- Telegram Bot Token
- Telegram API credentials
- Assistant account string sessions

## Configuration
The bot uses environment variables for configuration:
- `BOT_TOKEN`: Telegram bot token
- `API_ID` & `API_HASH`: Telegram API credentials
- `STRING_SESSIONS`: Assistant account sessions
- `OWNER_ID`: Bot owner user ID
- `LOG_GROUP_ID`: Logging group ID

## Security Features
- User authentication and authorization
- Rate limiting and spam protection
- Secure session management
- Admin-only sensitive commands
- Blacklist system for problematic users/chats

## Performance
- Optimized for high concurrent usage
- Efficient memory management
- Fast response times
- Minimal resource usage
- Stable long-running operation

---

*This documentation covers the core functionality of Amusic Bot. For detailed command usage and advanced configuration, refer to the inline help system within the bot.*
