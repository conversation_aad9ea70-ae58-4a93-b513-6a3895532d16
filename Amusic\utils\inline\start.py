#
#

from pyrogram.types import Inline<PERSON><PERSON>boardButton

from config import G<PERSON><PERSON><PERSON><PERSON>_REPO, SUPPORT_CHANNEL, SUPPORT_GROUP
from Amusic import app


# def start_pannel(_):
    # buttons = [
        # [
            # InlineKeyboardButton(
                # text=_["S_B_1"],
                # url=f"https://t.me/{app.username}?start=help",
            # ),
            # InlineKeyboardButton(text=_["S_B_2"], callback_data="settings_helper"),
        # ],
    # ]
    # if SUPPORT_CHANNEL and SUPPORT_GROUP:
        # buttons.append(
            # [
                # InlineKeyboardButton(text=_["S_B_4"], url=f"{SUPPORT_CHANNEL}"),
                # InlineKeyboardButton(text=_["S_B_3"], url=f"{SUPPORT_GROUP}"),
            # ]
        # )
    # else:
        # if SUPPORT_CHANNEL:
            # buttons.append(
                # [InlineKeyboardButton(text=_["S_B_4"], url=f"{SUPPORT_CHANNEL}")]
            # )
        # if SUPPORT_GROUP:
            # buttons.append(
                # [InlineKeyboardButton(text=_["S_B_3"], url=f"{SUPPORT_GROUP}")]
            # )
    # return buttons

def start_pannel(_):
    buttons = [
        [
            InlineKeyboardButton(
                text=_["S_B_1"],
                url=f"https://t.me/{app.username}?start=help",
            ),
            InlineKeyboardButton(text=_["S_B_2"], callback_data="settings_helper"),
        ]
    ]
    return buttons


# def private_panel(_, BOT_USERNAME, OWNER: bool | int = None):
    # buttons = [
        # [InlineKeyboardButton(text=_["S_B_8"], callback_data="settings_back_helper")]
    # ]
    # if SUPPORT_CHANNEL and SUPPORT_GROUP:
        # buttons.append(
            # [
                # InlineKeyboardButton(text=_["S_B_4"], url=f"{SUPPORT_CHANNEL}"),
                # InlineKeyboardButton(text=_["S_B_3"], url=f"{SUPPORT_GROUP}"),
            # ]
        # )
    # else:
        # if SUPPORT_CHANNEL:
            # buttons.append(
                # [InlineKeyboardButton(text=_["S_B_4"], url=f"{SUPPORT_CHANNEL}")]
            # )
        # if SUPPORT_GROUP:
            # buttons.append(
                # [InlineKeyboardButton(text=_["S_B_3"], url=f"{SUPPORT_GROUP}")]
            # )
    # buttons.append(
        # [
            # InlineKeyboardButton(
                # text=_["S_B_5"],
                # url=f"https://t.me/{BOT_USERNAME}?startgroup=true",
            # )
        # ]
    # )
    # if GITHUB_REPO and OWNER:
        # buttons.append(
            # [
                # InlineKeyboardButton(text=_["S_B_7"], user_id=OWNER),
                # InlineKeyboardButton(text=_["S_B_6"], url=f"{GITHUB_REPO}"),
            # ]
        # )
    # else:
        # if GITHUB_REPO:
            # buttons.append(
                # [
                    # InlineKeyboardButton(text=_["S_B_6"], url=f"{GITHUB_REPO}"),
                # ]
            # )

        # if OWNER:
            # buttons.append(
                # [
                    # InlineKeyboardButton(text=_["S_B_7"], user_id=OWNER),
                # ]
            # )
    # buttons.append([InlineKeyboardButton(text=_["ST_B_6"], callback_data="LG")])
    # return buttons


def private_panel(_, BOT_USERNAME, OWNER: bool | int = None):
    buttons = [
        [InlineKeyboardButton(text=_["S_B_8"], callback_data="settings_back_helper")]
    ]
    buttons.append(
        [
            InlineKeyboardButton(
                text=_["S_B_5"],
                url=f"https://t.me/{BOT_USERNAME}?startgroup=true",
            )
        ]
    )
    buttons.append([InlineKeyboardButton(text=_["ST_B_6"], callback_data="LG")])
    return buttons