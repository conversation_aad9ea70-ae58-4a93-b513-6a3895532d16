#
# Redis implementation for local database with bot ID isolation
import json
import logging
import sys
from typing import Any, Dict, Optional

import redis.asyncio as redis

import config

logger = logging.getLogger(__name__)

# Redis connection
redis_client = None

class RedisDatabase:
    def __init__(self, bot_id: str = "default"):
        self.bot_id = bot_id
        self.prefix = f"amusic:{bot_id}:"

    def _get_key(self, collection: str, doc_id: str = None) -> str:
        """Generate Redis key with bot ID isolation"""
        if doc_id:
            return f"{self.prefix}{collection}:{doc_id}"
        return f"{self.prefix}{collection}"

    async def find_one(self, collection: str, query: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Find one document in collection"""
        if not redis_client:
            return None

        # For simple queries, use the first key-value pair as identifier
        if query:
            key_field = list(query.keys())[0]
            key_value = str(query[key_field])
            redis_key = self._get_key(collection, key_value)

            data = await redis_client.get(redis_key)
            if data:
                return json.loads(data)
        return None

    async def update_one(self, collection: str, query: Dict[str, Any], update: Dict[str, Any], upsert: bool = False) -> bool:
        """Update one document in collection"""
        if not redis_client:
            return False

        if query:
            key_field = list(query.keys())[0]
            key_value = str(query[key_field])
            redis_key = self._get_key(collection, key_value)

            # Get existing data or create new
            existing_data = await redis_client.get(redis_key)
            if existing_data:
                doc = json.loads(existing_data)
            elif upsert:
                doc = query.copy()
            else:
                return False

            # Apply $set operations
            if "$set" in update:
                doc.update(update["$set"])

            # Save back to Redis
            await redis_client.set(redis_key, json.dumps(doc))
            return True
        return False

    async def insert_one(self, collection: str, document: Dict[str, Any]) -> bool:
        """Insert one document into collection"""
        if not redis_client:
            return False

        # Use first field as identifier
        if document:
            key_field = list(document.keys())[0]
            key_value = str(document[key_field])
            redis_key = self._get_key(collection, key_value)

            await redis_client.set(redis_key, json.dumps(document))
            return True
        return False

    async def delete_one(self, collection: str, query: Dict[str, Any]) -> bool:
        """Delete one document from collection"""
        if not redis_client:
            return False

        if query:
            key_field = list(query.keys())[0]
            key_value = str(query[key_field])
            redis_key = self._get_key(collection, key_value)

            result = await redis_client.delete(redis_key)
            return result > 0
        return False

# Initialize Redis connection
async def init_redis():
    global redis_client
    try:
        redis_client = redis.Redis(
            host='localhost',
            port=6379,
            decode_responses=False,
            socket_connect_timeout=5,
            socket_timeout=5
        )
        await redis_client.ping()
        logger.info("Redis connection established successfully")
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {e}")
        logger.error("Please ensure Redis is running on localhost:6379")
        sys.exit(1)

# Create database instance with bot ID isolation
BOT_ID = getattr(config, 'BOT_TOKEN', 'default').split(':')[0] if hasattr(config, 'BOT_TOKEN') and config.BOT_TOKEN else 'default'
redis_db = RedisDatabase(BOT_ID)

# Compatibility layer for existing MongoDB code
class MongoCompatibility:
    def __init__(self, db_instance):
        self.db = db_instance

    @property
    def sudoers(self):
        return CollectionWrapper(self.db, "sudoers")

    @property
    def users(self):
        return CollectionWrapper(self.db, "users")

    @property
    def chats(self):
        return CollectionWrapper(self.db, "chats")

    @property
    def playlists(self):
        return CollectionWrapper(self.db, "playlists")

class CollectionWrapper:
    def __init__(self, db_instance, collection_name):
        self.db = db_instance
        self.collection_name = collection_name

    async def find_one(self, query):
        return await self.db.find_one(self.collection_name, query)

    async def update_one(self, query, update, upsert=False):
        return await self.db.update_one(self.collection_name, query, update, upsert)

    async def insert_one(self, document):
        return await self.db.insert_one(self.collection_name, document)

    async def delete_one(self, query):
        return await self.db.delete_one(self.collection_name, query)

# Create compatibility instance
mongodb = MongoCompatibility(redis_db)
