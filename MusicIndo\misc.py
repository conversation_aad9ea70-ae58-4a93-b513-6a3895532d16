#


import logging
import time

# heroku3 import removed for Ubuntu server deployment
from pyrogram import filters

import config

SUDOERS = filters.user()


# HAPP removed for Ubuntu server deployment
_boot_ = time.time()
logger = logging.getLogger(__name__)


# Heroku detection and XCB list removed for Ubuntu server deployment


def dbb():
    global db
    db = {}
    logger.info(f"Database Initialized.")


async def sudo():
    # Load sudoers from Redis database
    from MusicIndo.core.mongo import mongodb

    # Always load owner IDs first
    for user_id in config.OWNER_ID:
        SUDOERS.add(user_id)

    # Load additional sudoers from database
    try:
        sudoersdb = mongodb.sudoers
        db_sudoers = await sudoersdb.find_one({"sudo": "sudo"})
        db_sudoers = [] if not db_sudoers else db_sudoers["sudoers"]

        # Add owner IDs to database if not present
        for user_id in config.OWNER_ID:
            if user_id not in db_sudoers:
                db_sudoers.append(user_id)
                await sudoersdb.update_one(
                    {"sudo": "sudo"},
                    {"$set": {"sudoers": db_sudoers}},
                    upsert=True,
                )

        # Add all database sudoers to filter
        if db_sudoers:
            for x in db_sudoers:
                SUDOERS.add(x)
    except Exception as e:
        logger.warning(f"Failed to load sudoers from database: {e}")

    logger.info("Sudoers Loaded.")


# Heroku function removed for Ubuntu server deployment
def heroku():
    # No-op function for compatibility
    logger.info("Heroku configuration skipped - Ubuntu server deployment")
