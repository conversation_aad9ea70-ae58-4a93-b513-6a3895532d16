#


import logging
import time

# heroku3 import removed for Ubuntu server deployment
from pyrogram import filters

import config
from MusicIndo.core.mongo import mongodb

SUDOERS = filters.user()


# HAPP removed for Ubuntu server deployment
_boot_ = time.time()
logger = logging.getLogger(__name__)


# Heroku detection and XCB list removed for Ubuntu server deployment


def dbb():
    global db
    db = {}
    logger.info(f"Database Initialized.")


async def sudo():
    if config.MONGO_DB_URI is None:
        for user_id in config.OWNER_ID:
            SUDOERS.add(user_id)
    else:
        sudoersdb = mongodb.sudoers
        db_sudoers = await sudoersdb.find_one({"sudo": "sudo"})
        db_sudoers = [] if not db_sudoers else db_sudoers["sudoers"]
        for user_id in config.OWNER_ID:
            SUDOERS.add(user_id)
            if user_id not in db_sudoers:
                db_sudoers.append(user_id)
                await sudoersdb.update_one(
                    {"sudo": "sudo"},
                    {"$set": {"sudoers": db_sudoers}},
                    upsert=True,
                )
        if db_sudoers:
            for x in db_sudoers:
                SUDOERS.add(x)

    logger.info("Sudoers Loaded.")


# Heroku function removed for Ubuntu server deployment
def heroku():
    # No-op function for compatibility
    logger.info("Heroku configuration skipped - Ubuntu server deployment")
