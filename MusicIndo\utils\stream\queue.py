#
#


from config import autoclean, chatstats, userstats
from config.config import time_to_seconds
from MusicIndo.misc import db


async def put_queue(
    chat_id,
    original_chat_id,
    file,
    title,
    duration,
    user,
    vidid,
    user_id,
    stream,
    url: str = None,
    forceplay: bool | str = None,
):
    title = title.title()
    try:
        duration_in_seconds = time_to_seconds(duration) - 3
    except Exception:
        duration_in_seconds = 0
    put = {
        "title": title,
        "dur": duration,
        "streamtype": stream,
        "by": user,
        "chat_id": original_chat_id,
        "file": file,
        "vidid": vidid,
        "seconds": duration_in_seconds,
        "played": 0,
        "url": url,
    }
    if forceplay:
        if check := db.get(chat_id):
            check.insert(0, put)
        else:
            db[chat_id] = []
            db[chat_id].append(put)
    else:
        db[chat_id].append(put)
    autoclean.append(file)
    vidid = "telegram" if vidid == "soundcloud" or "saavn" in vidid else vidid

    to_append = {"vidid": vidid, "title": title}
    if chat_id not in chatstats:
        chatstats[chat_id] = []
    chatstats[chat_id].append(to_append)
    if user_id not in userstats:
        userstats[user_id] = []
    userstats[user_id].append(to_append)
    return


async def put_queue_index(
    chat_id,
    original_chat_id,
    file,
    title,
    duration,
    user,
    vidid,
    stream,
    forceplay: bool | str = None,
):
    put = {
        "title": title,
        "dur": duration,
        "streamtype": stream,
        "by": user,
        "chat_id": original_chat_id,
        "file": file,
        "vidid": vidid,
        "seconds": 0,
        "played": 0,
    }
    if forceplay:
        if check := db.get(chat_id):
            check.insert(0, put)
        else:
            db[chat_id] = []
            db[chat_id].append(put)
    else:
        db[chat_id].append(put)
