# سكريبتات اختبار كلاس اليوتيوب
# YouTube Class Test Scripts

هذا المجلد يحتوي على سكريبتات اختبار شاملة لكلاس اليوتيوب في مشروع Amusic.

## الملفات المتوفرة

### 1. `quick_youtube_test.py` - الاختبار السريع
اختبار بسيط وسريع للتأكد من عمل الوظائف الأساسية لكلاس اليوتيوب.

**الاستخدام:**
```bash
python quick_youtube_test.py
```

**ما يختبره:**
- استيراد كلاس اليوتيوب
- إنشاء كائن من الكلاس
- دالة `exists()` للتحقق من وجود الفيديو
- دالة `title()` للحصول على عنوان الفيديو
- دالة `duration()` للحصول على مدة الفيديو
- دالة `details()` للحصول على تفاصيل الفيديو

### 2. `test_youtube_class.py` - الاختبار الشامل
اختبار شامل لجميع وظائف كلاس اليوتيوب مع تقرير مفصل.

**الاستخدام:**
```bash
python test_youtube_class.py
```

**ما يختبره:**
- جميع دوال كلاس اليوتيوب
- اختبار مع فيديوهات مختلفة
- اختبار قوائم التشغيل
- إنشاء تقرير JSON مفصل
- إحصائيات النجاح والفشل

**المخرجات:**
- تقرير مفصل على الشاشة
- ملف JSON يحتوي على جميع النتائج
- إحصائيات الأداء

### 3. `test_youtube_advanced.py` - الاختبار المتقدم
اختبار متقدم يركز على الأداء والحالات الخاصة.

**الاستخدام:**
```bash
python test_youtube_advanced.py
```

**ما يختبره:**
- أشكال مختلفة من روابط اليوتيوب
- أداء الكاش (Cache Performance)
- الطلبات المتزامنة (Concurrent Requests)
- التعامل مع الأخطاء
- حدود قوائم التشغيل
- أشكال الفيديو المختلفة
- تقرير أداء مفصل

## المتطلبات

### المكتبات المطلوبة:
- `asyncio` (مدمجة في Python)
- `sys` (مدمجة في Python)
- `os` (مدمجة في Python)
- `json` (مدمجة في Python)
- `time` (مدمجة في Python)
- `datetime` (مدمجة في Python)

### متطلبات المشروع:
- يجب أن تكون ملفات مشروع Amusic موجودة
- يجب أن يكون ملف `config.json` موجود ومُعد بشكل صحيح
- يجب أن تكون مكتبات اليوتيوب مثبتة (`yt-dlp`, `py-yt`, إلخ)

## كيفية الاستخدام

### 1. التحضير
```bash
# تأكد من وجودك في مجلد المشروع
cd /path/to/A-music

# تأكد من تثبيت المتطلبات
pip install -r requirements.txt
```

### 2. تشغيل الاختبار السريع
```bash
python quick_youtube_test.py
```

### 3. تشغيل الاختبار الشامل
```bash
python test_youtube_class.py
```

### 4. تشغيل الاختبار المتقدم
```bash
python test_youtube_advanced.py
```

## فهم النتائج

### رموز الحالة:
- ✅ **نجح**: الاختبار تم بنجاح
- ❌ **فشل**: الاختبار فشل
- ⚠️ **تحذير**: هناك مشكلة لكن ليست خطيرة
- 🔍 **معلومات**: معلومات إضافية
- 📊 **إحصائيات**: بيانات الأداء

### أنواع الأخطاء الشائعة:

#### 1. خطأ الاستيراد
```
❌ خطأ في استيراد كلاس اليوتيوب: No module named 'Amusic'
```
**الحل**: تأكد من وجودك في مجلد المشروع الصحيح

#### 2. خطأ الاتصال
```
❌ خطأ في الاختبار: Network error
```
**الحل**: تأكد من اتصال الإنترنت

#### 3. خطأ الكوكيز
```
❌ خطأ في الاختبار: Cookies file not found
```
**الحل**: تأكد من إعداد ملفات الكوكيز في مجلد `config/cookies`

## نصائح للاستخدام

### 1. اختبار سريع أولاً
ابدأ دائماً بالاختبار السريع للتأكد من عمل الأساسيات:
```bash
python quick_youtube_test.py
```

### 2. مراقبة الأداء
استخدم الاختبار المتقدم لمراقبة أداء الكلاس:
```bash
python test_youtube_advanced.py
```

### 3. التقارير المفصلة
استخدم الاختبار الشامل للحصول على تقارير JSON مفصلة:
```bash
python test_youtube_class.py
```

### 4. اختبار دوري
قم بتشغيل الاختبارات بشكل دوري للتأكد من استمرار عمل الكلاس.

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **مشكلة المسار**
   ```bash
   export PYTHONPATH="${PYTHONPATH}:/path/to/A-music"
   ```

2. **مشكلة الصلاحيات**
   ```bash
   chmod +x *.py
   ```

3. **مشكلة المكتبات**
   ```bash
   pip install --upgrade -r requirements.txt
   ```

## معلومات إضافية

- جميع الاختبارات تعمل بشكل غير متزامن (async)
- الاختبارات تستخدم فيديوهات حقيقية من يوتيوب
- النتائج تُحفظ في ملفات JSON للمراجعة اللاحقة
- الاختبارات آمنة ولا تؤثر على البيانات الأصلية

## الدعم

إذا واجهت أي مشاكل:
1. تأكد من إعداد المشروع بشكل صحيح
2. تحقق من اتصال الإنترنت
3. راجع ملفات السجل (logs)
4. تأكد من تحديث المكتبات

---

**ملاحظة**: هذه الاختبارات مصممة للتطوير والاختبار فقط. لا تستخدمها في بيئة الإنتاج بكثافة عالية.
