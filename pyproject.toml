[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "amusic"
version = "1.5.0"
description = "Amusic - A powerful music streaming bot for Telegram voice chats."
readme = "README.md"
requires-python = ">=3.10"
license = "MIT"
license-files = ["LICENSE"]
authors = [
  { name = "Amusic Team" }
]
keywords = ["telegram", "music", "bot", "pyrogram", "pytgcalls"]
classifiers = [
  "Environment :: Console",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13",
  "Topic :: Utilities",
]

dependencies = [
    "aiofiles>=24.1.0",
    "aiohttp>=3.12.13",
    "async-lru>=2.0.5",
    "beautifulsoup4>=4.13.4",
    "flask>=3.1.1",
    "gunicorn>=23.0.0",
    "kurigram>=2.2.6",
    "lyricsgenius>=3.6.4",
    "ntgcalls>=2.0.5",
    "pillow>=11.3.0",
    "psutil>=7.0.0",
    "py-tgcalls>=2.2.5",
    "py-yt-search>=0.3",
    "pykeyboard2>=0.1.6",
    "python-dotenv>=1.1.1",
    "pyyaml>=6.0.2",
    "redis>=5.0.0",
    "speedtest-cli>=2.1.3",
    "spotipy>=2.25.1",
    "tgcrypto>=1.2.5",
    "uvloop>=0.21.0",
    "yt-dlp>=2025.6.30",
]


[tool.setuptools.packages.find]
include = ["MusicIndo", "config", "strings"]

[project.scripts]
amusic = "MusicIndo.__main__:main"
