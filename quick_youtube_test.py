#!/usr/bin/env python3
"""
اختبار سريع لكلاس اليوتيوب
Quick YouTube Class Test

اختبار بسيط وسريع للتأكد من عمل كلاس اليوتيوب
Simple and quick test to verify YouTube class functionality
"""

import asyncio
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# تجنب مشكلة uvloop على Windows
import platform
if platform.system() == "Windows":
    # تعطيل uvloop على Windows
    import importlib.util
    spec = importlib.util.find_spec("uvloop")
    if spec is not None:
        sys.modules['uvloop'] = None

async def quick_test():
    """اختبار سريع"""
    print("🚀 بدء الاختبار السريع لكلاس اليوتيوب")
    print("🚀 Starting quick YouTube class test")
    
    try:
        # استيراد الكلاس
        from Amusic.platforms.youtube import YouTube
        print("✅ تم استيراد كلاس اليوتيوب بنجاح")
        
        # إنشاء كائن من الكلاس
        youtube = YouTube()
        print("✅ تم إنشاء كائن اليوتيوب بنجاح")
        
        # اختبار رابط بسيط
        test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        print(f"\n🔗 اختبار الرابط: {test_url}")
        
        # اختبار exists
        print("🔍 اختبار دالة exists...")
        exists_result = await youtube.exists(test_url)
        print(f"   النتيجة: {exists_result}")
        
        if exists_result:
            # اختبار title
            print("🔍 اختبار دالة title...")
            title_result = await youtube.title(test_url)
            print(f"   العنوان: {title_result}")
            
            # اختبار duration
            print("🔍 اختبار دالة duration...")
            duration_result = await youtube.duration(test_url)
            print(f"   المدة: {duration_result}")
            
            # اختبار details
            print("🔍 اختبار دالة details...")
            details_result = await youtube.details(test_url)
            print(f"   التفاصيل: العنوان={details_result[0]}, المدة={details_result[1]}")
        
        print("\n✅ انتهى الاختبار السريع بنجاح!")
        print("✅ Quick test completed successfully!")
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من وجود ملفات المشروع في المسار الصحيح")
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        print(f"❌ Test error: {e}")

if __name__ == "__main__":
    asyncio.run(quick_test())
