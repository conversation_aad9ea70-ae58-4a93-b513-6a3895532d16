#!/usr/bin/env python3
"""
اختبار مبسط لكلاس اليوتيوب
Simple YouTube Class Test

اختبار مبسط يتجنب مشاكل الاستيراد المعقدة
Simple test that avoids complex import issues
"""

import asyncio
import sys
import os
import platform

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print("🚀 بدء الاختبار المبسط لكلاس اليوتيوب")
print("🚀 Starting simple YouTube class test")
print(f"🖥️  النظام: {platform.system()}")
print(f"🐍 Python: {sys.version}")

async def simple_test():
    """اختبار مبسط"""
    
    try:
        print("\n📦 محاولة استيراد المكتبات الأساسية...")
        
        # اختبار استيراد المكتبات الأساسية أولاً
        try:
            import json
            print("✅ json - تم الاستيراد")
        except ImportError as e:
            print(f"❌ json - فشل: {e}")
            return
        
        try:
            import re
            print("✅ re - تم الاستيراد")
        except ImportError as e:
            print(f"❌ re - فشل: {e}")
            return
        
        try:
            from pyrogram import filters
            print("✅ pyrogram - تم الاستيراد")
        except ImportError as e:
            print(f"❌ pyrogram - فشل: {e}")
            print("💡 تأكد من تثبيت pyrogram: pip install pyrogram")
            return
        
        # محاولة استيراد config
        try:
            print("\n📁 محاولة استيراد config...")
            import config
            print("✅ config - تم الاستيراد")
            
            # اختبار بعض المتغيرات
            if hasattr(config, 'API_ID'):
                print(f"✅ API_ID موجود: {config.API_ID}")
            else:
                print("⚠️  API_ID غير موجود")
                
            if hasattr(config, 'BOT_TOKEN'):
                print("✅ BOT_TOKEN موجود")
            else:
                print("⚠️  BOT_TOKEN غير موجود")
                
        except ImportError as e:
            print(f"❌ config - فشل: {e}")
            print("💡 تأكد من وجود ملف config.json")
            return
        
        # محاولة استيراد كلاس اليوتيوب
        try:
            print("\n🎬 محاولة استيراد كلاس اليوتيوب...")
            from Amusic.platforms.youtube import YouTube
            print("✅ YouTube class - تم الاستيراد بنجاح")
        except ImportError as e:
            print(f"❌ YouTube class - فشل: {e}")
            print("💡 تأكد من وجود ملف Amusic/platforms/youtube.py")
            return
        
        # إنشاء كائن من الكلاس
        try:
            print("\n🏗️  إنشاء كائن اليوتيوب...")
            youtube = YouTube()
            print("✅ تم إنشاء كائن اليوتيوب بنجاح")
            
            # اختبار الخصائص الأساسية
            print(f"🔗 Base URL: {youtube.base}")
            print(f"📝 Regex: {youtube.regex}")
            
        except Exception as e:
            print(f"❌ فشل في إنشاء كائن اليوتيوب: {e}")
            return
        
        # اختبار دالة exists مع رابط بسيط
        try:
            print("\n🔍 اختبار دالة exists...")
            test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
            print(f"🔗 الرابط المختبر: {test_url}")
            
            exists_result = await youtube.exists(test_url)
            print(f"📊 النتيجة: {exists_result}")
            
            if exists_result:
                print("✅ الرابط صحيح ويوتيوب يتعرف عليه")
            else:
                print("❌ الرابط غير صحيح أو لا يتعرف عليه يوتيوب")
                
        except Exception as e:
            print(f"❌ خطأ في اختبار exists: {e}")
            return
        
        # إذا نجح exists، جرب اختبارات أخرى
        if exists_result:
            try:
                print("\n📝 اختبار دالة title...")
                title_result = await youtube.title(test_url)
                print(f"📊 العنوان: {title_result}")
                
            except Exception as e:
                print(f"❌ خطأ في اختبار title: {e}")
            
            try:
                print("\n⏱️  اختبار دالة duration...")
                duration_result = await youtube.duration(test_url)
                print(f"📊 المدة: {duration_result}")
                
            except Exception as e:
                print(f"❌ خطأ في اختبار duration: {e}")
        
        print("\n🎉 انتهى الاختبار المبسط!")
        print("🎉 Simple test completed!")
        
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        print(f"❌ General test error: {e}")
        import traceback
        traceback.print_exc()

def check_dependencies():
    """فحص المتطلبات الأساسية"""
    print("\n🔍 فحص المتطلبات الأساسية...")
    
    required_packages = [
        'pyrogram',
        'yt-dlp', 
        'py-yt',
        'async-lru',
        'redis'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} - مثبت")
        except ImportError:
            print(f"❌ {package} - غير مثبت")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  المكتبات المفقودة: {', '.join(missing_packages)}")
        print("💡 لتثبيتها:")
        for package in missing_packages:
            print(f"   pip install {package}")
        return False
    else:
        print("\n✅ جميع المتطلبات الأساسية مثبتة")
        return True

def check_files():
    """فحص الملفات المطلوبة"""
    print("\n📁 فحص الملفات المطلوبة...")
    
    required_files = [
        'config.json',
        'Amusic/__init__.py',
        'Amusic/platforms/__init__.py',
        'Amusic/platforms/youtube.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} - موجود")
        else:
            print(f"❌ {file_path} - مفقود")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️  الملفات المفقودة: {', '.join(missing_files)}")
        return False
    else:
        print("\n✅ جميع الملفات المطلوبة موجودة")
        return True

async def main():
    """الدالة الرئيسية"""
    print("="*60)
    print("🧪 اختبار كلاس اليوتيوب - مبسط")
    print("🧪 YouTube Class Test - Simple")
    print("="*60)
    
    # فحص المتطلبات
    deps_ok = check_dependencies()
    files_ok = check_files()
    
    if not deps_ok or not files_ok:
        print("\n❌ لا يمكن المتابعة بسبب متطلبات مفقودة")
        print("❌ Cannot continue due to missing requirements")
        return
    
    # تشغيل الاختبار
    await simple_test()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  تم إيقاف الاختبار بواسطة المستخدم")
        print("\n⚠️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الاختبار: {e}")
        print(f"\n❌ Error running test: {e}")
