#!/usr/bin/env python3
"""
سكريبت اختبار متقدم لكلاس اليوتيوب
Advanced YouTube Class Test Script

هذا السكريبت يختبر الوظائف المتقدمة لكلاس اليوتيوب
This script tests advanced YouTube class functions
"""

import asyncio
import sys
import os
import time
from datetime import datetime

# إضافة مسار المشروع للـ Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from Amusic.platforms.youtube import YouTube
    print("✅ تم استيراد كلاس اليوتيوب بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد كلاس اليوتيوب: {e}")
    sys.exit(1)

class AdvancedYouTubeTester:
    """فئة اختبار متقدمة لكلاس اليوتيوب"""
    
    def __init__(self):
        self.youtube = YouTube()
        self.performance_results = {}
    
    def print_header(self, title):
        """طباعة رأس القسم"""
        print("\n" + "🔥"*20)
        print(f"🎯 {title}")
        print("🔥"*20)
    
    async def test_performance(self, method_name, method_func, *args, **kwargs):
        """اختبار أداء الدالة"""
        print(f"\n⏱️  اختبار أداء: {method_name}")
        
        start_time = time.time()
        try:
            result = await method_func(*args, **kwargs)
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"✅ نجح في {execution_time:.2f} ثانية")
            self.performance_results[method_name] = {
                "success": True,
                "time": execution_time,
                "result_type": type(result).__name__
            }
            return result
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"❌ فشل في {execution_time:.2f} ثانية: {e}")
            self.performance_results[method_name] = {
                "success": False,
                "time": execution_time,
                "error": str(e)
            }
            return None
    
    async def test_different_url_formats(self):
        """اختبار أشكال مختلفة من روابط اليوتيوب"""
        self.print_header("اختبار أشكال مختلفة من الروابط")
        
        test_urls = [
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            "https://youtu.be/dQw4w9WgXcQ",
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=30s",
            "https://m.youtube.com/watch?v=dQw4w9WgXcQ",
            "dQw4w9WgXcQ"  # معرف الفيديو فقط
        ]
        
        for i, url in enumerate(test_urls, 1):
            print(f"\n🔗 اختبار الرابط {i}: {url}")
            
            # اختبار exists
            if url == "dQw4w9WgXcQ":
                await self.test_performance(f"exists_format_{i}", self.youtube.exists, url, videoid=True)
            else:
                await self.test_performance(f"exists_format_{i}", self.youtube.exists, url)
    
    async def test_cache_performance(self):
        """اختبار أداء الكاش"""
        self.print_header("اختبار أداء الكاش")
        
        test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        
        # الاستدعاء الأول (بدون كاش)
        print("\n🔄 الاستدعاء الأول (بدون كاش)")
        await self.test_performance("details_first_call", self.youtube.details, test_url)
        
        # الاستدعاء الثاني (مع الكاش)
        print("\n⚡ الاستدعاء الثاني (مع الكاش)")
        await self.test_performance("details_cached_call", self.youtube.details, test_url)
        
        # مقارنة الأداء
        if "details_first_call" in self.performance_results and "details_cached_call" in self.performance_results:
            first_time = self.performance_results["details_first_call"]["time"]
            cached_time = self.performance_results["details_cached_call"]["time"]
            improvement = ((first_time - cached_time) / first_time) * 100
            print(f"\n📊 تحسن الأداء بالكاش: {improvement:.1f}%")
    
    async def test_concurrent_requests(self):
        """اختبار الطلبات المتزامنة"""
        self.print_header("اختبار الطلبات المتزامنة")
        
        test_videos = [
            "dQw4w9WgXcQ",
            "jNQXAC9IVRw", 
            "9bZkp7q19f0",
            "kJQP7kiw5Fk",
            "tgbNymZ7vqY"
        ]
        
        print(f"\n🚀 تشغيل {len(test_videos)} طلبات متزامنة...")
        
        start_time = time.time()
        tasks = []
        for i, video_id in enumerate(test_videos):
            task = self.youtube.title(video_id, videoid=True)
            tasks.append(task)
        
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            successful = sum(1 for r in results if not isinstance(r, Exception))
            total_time = end_time - start_time
            
            print(f"✅ نجح {successful}/{len(test_videos)} طلبات في {total_time:.2f} ثانية")
            print(f"📈 متوسط الوقت لكل طلب: {total_time/len(test_videos):.2f} ثانية")
            
        except Exception as e:
            print(f"❌ خطأ في الطلبات المتزامنة: {e}")
    
    async def test_error_handling(self):
        """اختبار التعامل مع الأخطاء"""
        self.print_header("اختبار التعامل مع الأخطاء")
        
        error_cases = [
            ("رابط غير موجود", "https://www.youtube.com/watch?v=invalidvideo123"),
            ("رابط فيديو محذوف", "https://www.youtube.com/watch?v=deleted_video"),
            ("رابط غير صحيح", "https://not-youtube.com/watch?v=test"),
            ("معرف فيديو غير صحيح", "invalid_video_id"),
            ("رابط فارغ", "")
        ]
        
        for case_name, test_url in error_cases:
            print(f"\n🧪 اختبار: {case_name}")
            print(f"🔗 الرابط: {test_url}")
            
            try:
                if test_url == "invalid_video_id":
                    result = await self.youtube.exists(test_url, videoid=True)
                else:
                    result = await self.youtube.exists(test_url)
                print(f"📊 النتيجة: {result}")
            except Exception as e:
                print(f"⚠️  خطأ متوقع: {type(e).__name__}: {e}")
    
    async def test_playlist_limits(self):
        """اختبار حدود قوائم التشغيل"""
        self.print_header("اختبار حدود قوائم التشغيل")
        
        playlist_url = "https://youtube.com/playlist?list=PLrAXtmRdnEQy6nuLMt9xaJGA6H_VjlrBY"
        limits = [1, 5, 10, 20]
        
        for limit in limits:
            print(f"\n📋 اختبار قائمة تشغيل بحد {limit} فيديو")
            result = await self.test_performance(
                f"playlist_limit_{limit}", 
                self.youtube.playlist, 
                playlist_url, 
                limit
            )
            if result:
                print(f"📊 تم الحصول على {len(result)} فيديو")
    
    async def test_video_formats(self):
        """اختبار أشكال الفيديو المختلفة"""
        self.print_header("اختبار أشكال الفيديو")
        
        test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        
        print("\n🎬 اختبار الحصول على أشكال الفيديو...")
        formats = await self.test_performance("formats_test", self.youtube.formats, test_url)
        
        if formats:
            formats_list, _ = formats
            print(f"📊 تم العثور على {len(formats_list)} شكل متاح")
            
            # عرض أول 3 أشكال
            for i, fmt in enumerate(formats_list[:3], 1):
                print(f"   {i}. {fmt.get('format_note', 'N/A')} - {fmt.get('ext', 'N/A')}")
    
    def generate_performance_report(self):
        """إنشاء تقرير الأداء"""
        self.print_header("تقرير الأداء النهائي")
        
        if not self.performance_results:
            print("❌ لا توجد نتائج أداء للعرض")
            return
        
        successful_tests = [k for k, v in self.performance_results.items() if v["success"]]
        failed_tests = [k for k, v in self.performance_results.items() if not v["success"]]
        
        print(f"📊 إجمالي الاختبارات: {len(self.performance_results)}")
        print(f"✅ نجح: {len(successful_tests)}")
        print(f"❌ فشل: {len(failed_tests)}")
        
        if successful_tests:
            times = [self.performance_results[test]["time"] for test in successful_tests]
            avg_time = sum(times) / len(times)
            max_time = max(times)
            min_time = min(times)
            
            print(f"\n⏱️  إحصائيات الوقت:")
            print(f"   📈 أبطأ اختبار: {max_time:.2f} ثانية")
            print(f"   📉 أسرع اختبار: {min_time:.2f} ثانية")
            print(f"   📊 متوسط الوقت: {avg_time:.2f} ثانية")
        
        if failed_tests:
            print(f"\n❌ الاختبارات الفاشلة:")
            for test in failed_tests:
                error = self.performance_results[test].get("error", "خطأ غير معروف")
                print(f"   • {test}: {error}")
    
    async def run_advanced_tests(self):
        """تشغيل جميع الاختبارات المتقدمة"""
        print("🚀 بدء الاختبارات المتقدمة لكلاس اليوتيوب")
        print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        await self.test_different_url_formats()
        await self.test_cache_performance()
        await self.test_concurrent_requests()
        await self.test_error_handling()
        await self.test_playlist_limits()
        await self.test_video_formats()
        
        self.generate_performance_report()

async def main():
    """الدالة الرئيسية"""
    try:
        tester = AdvancedYouTubeTester()
        await tester.run_advanced_tests()
    except KeyboardInterrupt:
        print("\n⚠️  تم إيقاف الاختبار بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")

if __name__ == "__main__":
    asyncio.run(main())
