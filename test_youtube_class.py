#!/usr/bin/env python3
"""
سكريبت اختبار كلاس اليوتيوب
YouTube Class Test Script

هذا السكريبت يختبر جميع وظائف كلاس اليوتيوب بشكل منفصل
This script tests all YouTube class functions independently
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# إضافة مسار المشروع للـ Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# استيراد الكلاسات المطلوبة
try:
    from Amusic.platforms.youtube import YouTube
    from config import cookies
    print("✅ تم استيراد كلاس اليوتيوب بنجاح")
    print("✅ YouTube class imported successfully")
except ImportError as e:
    print(f"❌ خطأ في استيراد كلاس اليوتيوب: {e}")
    print(f"❌ Error importing YouTube class: {e}")
    sys.exit(1)

class YouTubeClassTester:
    """فئة اختبار كلاس اليوتيوب"""
    
    def __init__(self):
        self.youtube = YouTube()
        self.test_results = {}
        self.test_videos = [
            {
                "name": "فيديو قصير - Short Video",
                "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
                "video_id": "dQw4w9WgXcQ"
            },
            {
                "name": "فيديو طويل - Long Video", 
                "url": "https://www.youtube.com/watch?v=jNQXAC9IVRw",
                "video_id": "jNQXAC9IVRw"
            }
        ]
        self.test_playlists = [
            {
                "name": "قائمة تشغيل قصيرة - Short Playlist",
                "url": "https://youtube.com/playlist?list=PLrAXtmRdnEQy6nuLMt9xaJGA6H_VjlrBY",
                "playlist_id": "PLrAXtmRdnEQy6nuLMt9xaJGA6H_VjlrBY"
            }
        ]
    
    def print_separator(self, title):
        """طباعة فاصل مع العنوان"""
        print("\n" + "="*60)
        print(f"🔍 {title}")
        print("="*60)
    
    def print_result(self, test_name, success, result=None, error=None):
        """طباعة نتيجة الاختبار"""
        status = "✅ نجح" if success else "❌ فشل"
        print(f"{status} | {test_name}")
        
        if success and result is not None:
            if isinstance(result, (dict, list)):
                print(f"   📊 النتيجة: {json.dumps(result, indent=2, ensure_ascii=False)}")
            else:
                print(f"   📊 النتيجة: {result}")
        
        if not success and error:
            print(f"   ⚠️  الخطأ: {error}")
        
        self.test_results[test_name] = {
            "success": success,
            "result": result,
            "error": str(error) if error else None,
            "timestamp": datetime.now().isoformat()
        }
    
    async def test_exists_method(self):
        """اختبار دالة exists"""
        self.print_separator("اختبار دالة exists - Testing exists method")
        
        # اختبار رابط يوتيوب صحيح
        try:
            result = await self.youtube.exists("https://www.youtube.com/watch?v=dQw4w9WgXcQ")
            self.print_result("exists - رابط يوتيوب صحيح", True, result)
        except Exception as e:
            self.print_result("exists - رابط يوتيوب صحيح", False, error=e)
        
        # اختبار رابط غير صحيح
        try:
            result = await self.youtube.exists("https://www.google.com")
            self.print_result("exists - رابط غير صحيح", True, result)
        except Exception as e:
            self.print_result("exists - رابط غير صحيح", False, error=e)
        
        # اختبار معرف فيديو
        try:
            result = await self.youtube.exists("dQw4w9WgXcQ", videoid=True)
            self.print_result("exists - معرف فيديو", True, result)
        except Exception as e:
            self.print_result("exists - معرف فيديو", False, error=e)
    
    async def test_details_method(self):
        """اختبار دالة details"""
        self.print_separator("اختبار دالة details - Testing details method")
        
        for video in self.test_videos:
            try:
                result = await self.youtube.details(video["url"])
                self.print_result(f"details - {video['name']}", True, {
                    "title": result[0],
                    "duration": result[1],
                    "duration_sec": result[2],
                    "thumbnail": result[3][:50] + "...",
                    "video_id": result[4]
                })
            except Exception as e:
                self.print_result(f"details - {video['name']}", False, error=e)
    
    async def test_title_method(self):
        """اختبار دالة title"""
        self.print_separator("اختبار دالة title - Testing title method")
        
        for video in self.test_videos:
            try:
                result = await self.youtube.title(video["url"])
                self.print_result(f"title - {video['name']}", True, result)
            except Exception as e:
                self.print_result(f"title - {video['name']}", False, error=e)
    
    async def test_duration_method(self):
        """اختبار دالة duration"""
        self.print_separator("اختبار دالة duration - Testing duration method")
        
        for video in self.test_videos:
            try:
                result = await self.youtube.duration(video["url"])
                self.print_result(f"duration - {video['name']}", True, result)
            except Exception as e:
                self.print_result(f"duration - {video['name']}", False, error=e)
    
    async def test_thumbnail_method(self):
        """اختبار دالة thumbnail"""
        self.print_separator("اختبار دالة thumbnail - Testing thumbnail method")
        
        for video in self.test_videos:
            try:
                result = await self.youtube.thumbnail(video["url"])
                self.print_result(f"thumbnail - {video['name']}", True, result[:50] + "...")
            except Exception as e:
                self.print_result(f"thumbnail - {video['name']}", False, error=e)
    
    async def test_track_method(self):
        """اختبار دالة track"""
        self.print_separator("اختبار دالة track - Testing track method")
        
        for video in self.test_videos:
            try:
                result = await self.youtube.track(video["url"])
                track_details, vidid = result
                self.print_result(f"track - {video['name']}", True, {
                    "title": track_details["title"],
                    "vidid": vidid,
                    "duration": track_details["duration_min"]
                })
            except Exception as e:
                self.print_result(f"track - {video['name']}", False, error=e)
    
    async def test_playlist_method(self):
        """اختبار دالة playlist"""
        self.print_separator("اختبار دالة playlist - Testing playlist method")
        
        for playlist in self.test_playlists:
            try:
                result = await self.youtube.playlist(playlist["url"], limit=5)
                self.print_result(f"playlist - {playlist['name']}", True, {
                    "video_count": len(result),
                    "first_videos": result[:3] if result else []
                })
            except Exception as e:
                self.print_result(f"playlist - {playlist['name']}", False, error=e)
    
    async def test_video_method(self):
        """اختبار دالة video"""
        self.print_separator("اختبار دالة video - Testing video method")
        
        # اختبار فيديو واحد فقط لتوفير الوقت
        video = self.test_videos[0]
        try:
            result = await self.youtube.video(video["url"])
            status, url_or_error = result
            self.print_result(f"video - {video['name']}", status == 1, {
                "status": status,
                "result": url_or_error[:100] + "..." if len(str(url_or_error)) > 100 else url_or_error
            })
        except Exception as e:
            self.print_result(f"video - {video['name']}", False, error=e)
    
    def generate_report(self):
        """إنشاء تقرير شامل للاختبارات"""
        self.print_separator("تقرير الاختبارات النهائي - Final Test Report")
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results.values() if result["success"])
        failed_tests = total_tests - successful_tests
        
        print(f"📊 إجمالي الاختبارات: {total_tests}")
        print(f"✅ الاختبارات الناجحة: {successful_tests}")
        print(f"❌ الاختبارات الفاشلة: {failed_tests}")
        print(f"📈 معدل النجاح: {(successful_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n🔍 الاختبارات الفاشلة:")
            for test_name, result in self.test_results.items():
                if not result["success"]:
                    print(f"   ❌ {test_name}: {result['error']}")
        
        # حفظ التقرير في ملف JSON
        report_file = f"youtube_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, ensure_ascii=False)
            print(f"\n💾 تم حفظ التقرير المفصل في: {report_file}")
        except Exception as e:
            print(f"\n⚠️  فشل في حفظ التقرير: {e}")
    
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🚀 بدء اختبار كلاس اليوتيوب")
        print("🚀 Starting YouTube Class Tests")
        print(f"⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # تشغيل جميع الاختبارات
        await self.test_exists_method()
        await self.test_details_method()
        await self.test_title_method()
        await self.test_duration_method()
        await self.test_thumbnail_method()
        await self.test_track_method()
        await self.test_playlist_method()
        await self.test_video_method()
        
        # إنشاء التقرير النهائي
        self.generate_report()

async def main():
    """الدالة الرئيسية"""
    try:
        tester = YouTubeClassTester()
        await tester.run_all_tests()
    except KeyboardInterrupt:
        print("\n⚠️  تم إيقاف الاختبار بواسطة المستخدم")
        print("\n⚠️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        print(f"\n❌ General test error: {e}")

if __name__ == "__main__":
    # تشغيل الاختبارات
    asyncio.run(main())
